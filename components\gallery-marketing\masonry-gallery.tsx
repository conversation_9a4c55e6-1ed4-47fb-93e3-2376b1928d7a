"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, ZoomIn } from "lucide-react";
import { cn } from "@/lib/utils";
import { Image as ImageType } from "@/lib/models";
import ImageLightbox from "./image-lightbox";

interface MasonryGalleryProps {
  images: ImageType[];
  columns?: 2 | 3 | 4 | 5;
  gap?: number;
  className?: string;
  enableLightbox?: boolean;
  showLoadingStates?: boolean;
  onImageClick?: (image: ImageType, index: number) => void;
}

export default function MasonryGallery({
  images,
  columns = 3,
  gap = 16,
  className,
  enableLightbox = true,
  showLoadingStates = true,
  onImageClick,
}: MasonryGalleryProps) {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [columnHeights, setColumnHeights] = useState<number[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize column heights
  useEffect(() => {
    setColumnHeights(new Array(columns).fill(0));
  }, [columns]);

  // Handle image load
  const handleImageLoad = (imageId: string) => {
    setLoadedImages(prev => new Set([...prev, imageId]));
  };

  // Handle image click
  const handleImageClick = (image: ImageType, index: number) => {
    if (onImageClick) {
      onImageClick(image, index);
    } else if (enableLightbox) {
      setCurrentImageIndex(index);
      setLightboxOpen(true);
    }
  };

  // Lightbox navigation
  const handleNext = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const handlePrevious = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  // Calculate which column each image should go in
  const getColumnIndex = (index: number) => {
    return index % columns;
  };

  // Get responsive column count
  const getResponsiveColumns = () => {
    if (typeof window === 'undefined') return columns;
    
    const width = window.innerWidth;
    if (width < 640) return Math.min(2, columns);
    if (width < 768) return Math.min(2, columns);
    if (width < 1024) return Math.min(3, columns);
    return columns;
  };

  const [responsiveColumns, setResponsiveColumns] = useState(columns);

  useEffect(() => {
    const handleResize = () => {
      setResponsiveColumns(getResponsiveColumns());
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [columns]);

  // Create columns array
  const columnsArray = Array.from({ length: responsiveColumns }, () => [] as ImageType[]);
  
  // Distribute images across columns
  images.forEach((image, index) => {
    const columnIndex = getColumnIndex(index);
    columnsArray[columnIndex].push(image);
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  if (images.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <div className="text-center">
          <ZoomIn className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">No images to display</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <motion.div
        ref={containerRef}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className={cn("w-full", className)}
        style={{ gap }}
      >
        <div 
          className="flex items-start"
          style={{ gap }}
        >
          {columnsArray.map((columnImages, columnIndex) => (
            <div
              key={columnIndex}
              className="flex-1 flex flex-col"
              style={{ gap }}
            >
              {columnImages.map((image, imageIndex) => {
                const globalIndex = images.findIndex(img => img._id === image._id);
                const isLoaded = loadedImages.has(image._id as string);
                
                return (
                  <motion.div
                    key={image._id}
                    variants={itemVariants}
                    className="group relative overflow-hidden rounded-xl cursor-pointer"
                    onClick={() => handleImageClick(image, globalIndex)}
                  >
                    {/* Loading State */}
                    {showLoadingStates && !isLoaded && (
                      <div 
                        className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center z-10"
                        style={{ aspectRatio: image.width / image.height }}
                      >
                        <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
                      </div>
                    )}

                    {/* Image */}
                    <div className="relative">
                      <Image
                        src={image.url}
                        alt={image.name || `Image ${globalIndex + 1}`}
                        width={image.width}
                        height={image.height}
                        className={cn(
                          "w-full h-auto transition-all duration-500",
                          "group-hover:scale-105",
                          isLoaded ? "opacity-100" : "opacity-0"
                        )}
                        onLoad={() => handleImageLoad(image._id as string)}
                        sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      />

                      {/* Hover Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          whileHover={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.2 }}
                          className="opacity-0 group-hover:opacity-100"
                        >
                          <div className="bg-white/20 backdrop-blur-sm rounded-full p-3 border border-white/30">
                            <ZoomIn className="w-6 h-6 text-white" />
                          </div>
                        </motion.div>
                      </div>
                    </div>

                    {/* Image Info Overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <p className="text-white text-sm font-medium truncate">
                        {image.name || `Image ${globalIndex + 1}`}
                      </p>
                      <p className="text-white/70 text-xs">
                        {image.width} × {image.height}
                      </p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          ))}
        </div>
      </motion.div>

      {/* Lightbox */}
      {enableLightbox && (
        <ImageLightbox
          images={images}
          currentIndex={currentImageIndex}
          isOpen={lightboxOpen}
          onClose={() => setLightboxOpen(false)}
          onNext={handleNext}
          onPrevious={handlePrevious}
          animationType="slide"
          showThumbnails={true}
          enableZoom={true}
          enableSlideshow={true}
          enableDownload={true}
          enableShare={true}
          enableFullscreen={true}
        />
      )}
    </>
  );
}
