"use client";

import {
   AlbumCard,
   GalleryBreadcrumb,
   GalleryFilters,
   GalleryGrid,
   GallerySearch,
   LoadMoreButton,
} from "@/components/gallery-marketing";
import { But<PERSON> } from "@/components/ui/button";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { usePublicAlbums, useSearchAlbums } from "@/lib/hooks/use-albums";
import { motion } from "framer-motion";
import {
   Filter,
   Grid3X3,
   Search as SearchIcon,
   SortAsc,
   SortDesc,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";

const ITEMS_PER_PAGE = 12;

export default function AlbumsPageContent() {
   const [searchQuery, setSearchQuery] = useState("");
   const [currentPage, setCurrentPage] = useState(1);
   const [sortBy, setSortBy] = useState("createdAt");
   const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
   const [filters, setFilters] = useState<Record<string, string>>({});

   // Reset page when search or filters change
   useEffect(() => {
      setCurrentPage(1);
   }, [searchQuery, filters, sortBy, sortOrder]);

   // Fetch data based on search or normal listing
   const isSearching = searchQuery.trim().length > 0;

   const {
      data: albumsData,
      isLoading: albumsLoading,
      error: albumsError,
   } = usePublicAlbums({
      page: currentPage,
      limit: ITEMS_PER_PAGE,
      sortBy,
      sortOrder,
   });

   const { data: searchData, isLoading: searchLoading } = useSearchAlbums(
      searchQuery,
      {
         page: currentPage,
         limit: ITEMS_PER_PAGE,
         sortBy,
         sortOrder,
      }
   );

   // Determine which data to use
   const currentData = isSearching ? searchData : albumsData;
   const isLoading = isSearching ? searchLoading : albumsLoading;
   const albums = currentData?.data || [];
   const pagination = currentData?.pagination;

   // Breadcrumb items
   const breadcrumbItems = [
      { label: "Gallery", href: "/gallery" },
      { label: "Albums", isActive: true },
   ];

   // Filter options (these would typically come from your data or API)
   const dateOptions = [
      { id: "all", label: "All Time", value: "" },
      { id: "this-year", label: "This Year", value: "2024" },
      { id: "last-year", label: "Last Year", value: "2023" },
      { id: "older", label: "Older", value: "2022" },
   ];

   const categoryOptions = [
      { id: "all", label: "All Categories", value: "" },
      { id: "wedding", label: "Wedding", value: "wedding" },
      { id: "pre-wedding", label: "Pre-Wedding", value: "pre-wedding" },
      { id: "pregnancy", label: "Pregnancy", value: "pregnancy" },
      {
         id: "child-dedication",
         label: "Child Dedication",
         value: "child-dedication",
      },
   ];

   const handleLoadMore = () => {
      if (pagination?.hasNext) {
         setCurrentPage((prev) => prev + 1);
      }
   };

   const handleSortChange = (value: string) => {
      const [newSortBy, newSortOrder] = value.split("-");
      setSortBy(newSortBy);
      setSortOrder(newSortOrder as "asc" | "desc");
   };

   return (
      <div className="min-h-screen py-8">
         <div className="container">
            {/* Breadcrumb */}
            <GalleryBreadcrumb items={breadcrumbItems} />

            {/* Header */}
            <div className="mb-12">
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-8"
               >
                  <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4 flex items-center justify-center gap-3">
                     <Grid3X3 className="w-10 h-10 text-primary" />
                     Albums
                  </h1>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                     Browse through our complete collection of photography
                     albums, each telling a unique story through carefully
                     curated images.
                  </p>
               </motion.div>

               {/* Search */}
               <GallerySearch
                  onSearch={setSearchQuery}
                  placeholder="Search albums by name..."
                  className="mb-8"
               />

               {/* Controls */}
               <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                  {/* Filters */}
                  <GalleryFilters
                     onFiltersChange={setFilters}
                     dateOptions={dateOptions}
                     categoryOptions={categoryOptions}
                     showDateFilter={true}
                     showCategoryFilter={true}
                     showClientFilter={false}
                     className="flex-1"
                  />

                  {/* Sort */}
                  <div className="flex items-center gap-2">
                     <span className="text-sm text-muted-foreground">
                        Sort by:
                     </span>
                     <Select
                        value={`${sortBy}-${sortOrder}`}
                        onValueChange={handleSortChange}
                     >
                        <SelectTrigger className="w-48">
                           <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value="createdAt-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Newest First
                              </div>
                           </SelectItem>
                           <SelectItem value="createdAt-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Oldest First
                              </div>
                           </SelectItem>
                           <SelectItem value="name-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Name A-Z
                              </div>
                           </SelectItem>
                           <SelectItem value="name-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Name Z-A
                              </div>
                           </SelectItem>
                        </SelectContent>
                     </Select>
                  </div>
               </div>
            </div>

            {/* Results Info */}
            {isSearching && (
               <div className="mb-8">
                  {searchLoading ? (
                     <p className="text-muted-foreground">
                        Searching albums...
                     </p>
                  ) : (
                     <p className="text-muted-foreground">
                        {albums.length > 0 ? (
                           <>
                              Found {pagination?.total || albums.length} albums
                              for "
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              "
                           </>
                        ) : (
                           <>
                              No albums found for "
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              "
                           </>
                        )}
                     </p>
                  )}
               </div>
            )}

            {/* Albums Grid */}
            {isLoading ? (
               <GalleryGrid columns={4}>
                  {Array.from({ length: ITEMS_PER_PAGE }).map((_, i) => (
                     <div key={i} className="space-y-4">
                        <Skeleton className="aspect-[4/3] rounded-2xl" />
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                     </div>
                  ))}
               </GalleryGrid>
            ) : albums.length > 0 ? (
               <>
                  <GalleryGrid columns={4} className="mb-12">
                     {albums.map((album) => (
                        <AlbumCard
                           key={album._id}
                           album={album}
                           variant="default"
                           showStats={true}
                           showDescription={true}
                        />
                     ))}
                  </GalleryGrid>

                  {/* Pagination */}
                  {pagination && (
                     <div className="flex flex-col items-center gap-4">
                        {/* Load More Button */}
                        {pagination.hasNext && (
                           <LoadMoreButton
                              onClick={handleLoadMore}
                              isLoading={isLoading}
                              hasMore={pagination.hasNext}
                              buttonText="Load More Albums"
                              loadingText="Loading more albums..."
                           />
                        )}

                        {/* Pagination Info */}
                        <div className="text-sm text-muted-foreground text-center">
                           Showing {albums.length} of {pagination.total} albums
                           {pagination.totalPages > 1 && (
                              <span className="ml-2">
                                 (Page {pagination.page} of{" "}
                                 {pagination.totalPages})
                              </span>
                           )}
                        </div>
                     </div>
                  )}
               </>
            ) : (
               <div className="text-center py-20">
                  <Grid3X3 className="w-20 h-20 mx-auto mb-6 text-muted-foreground/50" />
                  <h3 className="text-2xl font-semibold text-foreground mb-4">
                     {isSearching ? "No Albums Found" : "No Albums Available"}
                  </h3>
                  <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                     {isSearching
                        ? "Try adjusting your search terms or filters to find what you're looking for."
                        : "We're working on adding new albums. Check back soon!"}
                  </p>
                  {isSearching && (
                     <Button
                        onClick={() => {
                           setSearchQuery("");
                           setFilters({});
                        }}
                        variant="outline"
                     >
                        Clear Search
                     </Button>
                  )}
               </div>
            )}
         </div>
      </div>
   );
}
