import { getAlbumById } from "@/lib/services/album-service";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import AlbumPageContent from "./album-page-content";

interface AlbumPageProps {
   params: {
      id: string;
   };
}

export async function generateMetadata({
   params,
}: AlbumPageProps): Promise<Metadata> {
   try {
      const album = await getAlbumById(params.id);

      if (!album) {
         return {
            title: "Album Not Found - Astral Studios",
            description: "The requested album could not be found.",
         };
      }

      return {
         title: `${album.name} - Albums - Astral Studios`,
         description:
            album.description ||
            `View the ${album.name} album featuring ${album.imageCount} stunning photographs by Astral Studios.`,
         keywords: ["album", "photography", album.name, "astral studios"],
         openGraph: {
            title: `${album.name} - Astral Studios`,
            description:
               album.description ||
               `View the ${album.name} album featuring ${album.imageCount} stunning photographs by Astral Studios.`,
            type: "website",
            images: album.coverImageUrl
               ? [
                    {
                       url: album.coverImageUrl,
                       width: 1200,
                       height: 630,
                       alt: album.name,
                    },
                 ]
               : [],
         },
         twitter: {
            card: "summary_large_image",
            title: `${album.name} - Astral Studios`,
            description:
               album.description ||
               `View the ${album.name} album featuring ${album.imageCount} stunning photographs by Astral Studios.`,
            images: album.coverImageUrl ? [album.coverImageUrl] : [],
         },
      };
   } catch (error) {
      console.error("Error generating metadata:", error);
      return {
         title: "Album - Astral Studios",
         description: "View album from Astral Studios photography collection.",
      };
   }
}

export default async function AlbumPage({ params }: AlbumPageProps) {
   const album = await getAlbumById(params.id);

   if (!album) {
      notFound();
   }

   return (
      <Suspense fallback={<div>Loading album...</div>}>
         <AlbumPageContent album={album} />
      </Suspense>
   );
}
