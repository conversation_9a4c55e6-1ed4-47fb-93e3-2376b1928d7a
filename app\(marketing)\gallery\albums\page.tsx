import { Metadata } from "next";
import { Suspense } from "react";
import AlbumsPageContent from "./albums-page-content";

export const metadata: Metadata = {
  title: "Albums - Gallery - Astral Studios",
  description: "Browse all our photography albums featuring weddings, pre-wedding shoots, pregnancy photography, and more professional photography services.",
  keywords: ["albums", "photography", "wedding albums", "pre-wedding", "pregnancy", "astral studios"],
  openGraph: {
    title: "Albums - Gallery - Astral Studios",
    description: "Browse all our photography albums featuring weddings, pre-wedding shoots, pregnancy photography, and more professional photography services.",
    type: "website",
    images: [
      {
        url: "/images/albums-hero.jpg",
        width: 1200,
        height: 630,
        alt: "Astral Studios Albums",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Albums - Gallery - Astral Studios",
    description: "Browse all our photography albums featuring weddings, pre-wedding shoots, pregnancy photography, and more professional photography services.",
    images: ["/images/albums-hero.jpg"],
  },
};

export default function AlbumsPage() {
  return (
    <Suspense fallback={<div>Loading albums...</div>}>
      <AlbumsPageContent />
    </Suspense>
  );
}
