"use client";

import { useState, useEffect } from "react";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface GallerySearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  className?: string;
  initialValue?: string;
  showClearButton?: boolean;
}

export default function GallerySearch({
  onSearch,
  placeholder = "Search albums and collections...",
  className,
  initialValue = "",
  showClearButton = true,
}: GallerySearchProps) {
  const [query, setQuery] = useState(initialValue);
  const [isFocused, setIsFocused] = useState(false);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearch(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query, onSearch]);

  const handleClear = () => {
    setQuery("");
    onSearch("");
  };

  return (
    <div className={cn("relative max-w-2xl mx-auto", className)}>
      <div
        className={cn(
          "relative flex items-center transition-all duration-300",
          isFocused
            ? "transform scale-105 shadow-lg shadow-primary/20"
            : "shadow-md"
        )}
      >
        {/* Search Icon */}
        <div className="absolute left-4 z-10">
          <Search
            className={cn(
              "w-5 h-5 transition-colors duration-200",
              isFocused ? "text-primary" : "text-muted-foreground"
            )}
          />
        </div>

        {/* Input Field */}
        <Input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className={cn(
            "pl-12 pr-12 py-6 text-lg rounded-2xl border-2 transition-all duration-300",
            "bg-background/80 backdrop-blur-sm",
            "focus:border-primary focus:ring-2 focus:ring-primary/20",
            "placeholder:text-muted-foreground/60",
            isFocused
              ? "border-primary bg-background"
              : "border-border hover:border-primary/50"
          )}
        />

        {/* Clear Button */}
        {showClearButton && query && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-2 z-10 h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive rounded-full"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Search Suggestions/Status */}
      {isFocused && (
        <div className="absolute top-full left-0 right-0 mt-2 p-4 bg-card border border-border rounded-xl shadow-lg z-20">
          <div className="text-sm text-muted-foreground">
            {query.trim() ? (
              <span>
                Searching for "<span className="text-foreground font-medium">{query}</span>"...
              </span>
            ) : (
              <span>Start typing to search albums and collections</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
