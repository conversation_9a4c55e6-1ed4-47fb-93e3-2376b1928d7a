import { Metadata } from "next";
import { Suspense } from "react";
import CollectionsPageContent from "./collections-page-content";

export const metadata: Metadata = {
  title: "Collections - Gallery - Astral Studios",
  description: "Explore our curated photography collections featuring different themes, styles, and artistic approaches to professional photography.",
  keywords: ["collections", "photography", "themes", "styles", "curated", "astral studios"],
  openGraph: {
    title: "Collections - Gallery - Astral Studios",
    description: "Explore our curated photography collections featuring different themes, styles, and artistic approaches to professional photography.",
    type: "website",
    images: [
      {
        url: "/images/collections-hero.jpg",
        width: 1200,
        height: 630,
        alt: "Astral Studios Collections",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Collections - Gallery - Astral Studios",
    description: "Explore our curated photography collections featuring different themes, styles, and artistic approaches to professional photography.",
    images: ["/images/collections-hero.jpg"],
  },
};

export default function CollectionsPage() {
  return (
    <Suspense fallback={<div>Loading collections...</div>}>
      <CollectionsPageContent />
    </Suspense>
  );
}
