import Link from "next/link";
import { ArrowLeft, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function AlbumNotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center py-8">
      <div className="container">
        <div className="max-w-md mx-auto text-center">
          <div className="w-20 h-20 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
            <Search className="w-10 h-10 text-muted-foreground" />
          </div>
          
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Album Not Found
          </h1>
          
          <p className="text-muted-foreground text-lg mb-8">
            The album you're looking for doesn't exist or may have been removed.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link href="/gallery/albums" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Back to Albums
              </Link>
            </Button>
            
            <Button asChild variant="outline">
              <Link href="/gallery">
                Browse Gallery
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
