"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Calendar, 
  Image as ImageIcon, 
  Share2, 
  Heart,
  ArrowLeft,
  Tag,
  Palette,
  Sparkles,
  Grid3X3
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useImages } from "@/lib/hooks/use-images";
import { CollectionWithStats } from "@/lib/models";
import {
  GalleryBreadcrumb,
  MasonryGallery,
} from "@/components/gallery-marketing";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface CollectionPageContentProps {
  collection: CollectionWithStats;
}

export default function CollectionPageContent({ collection }: CollectionPageContentProps) {
  const [isLiked, setIsLiked] = useState(false);

  // Fetch images for this collection
  const { 
    data: imagesData, 
    isLoading: imagesLoading, 
    error: imagesError 
  } = useImages({ 
    collectionId: collection._id as string,
    limit: 50 // Load more images for collection view
  });

  const images = imagesData?.data || [];

  // Breadcrumb items
  const breadcrumbItems = [
    { label: "Gallery", href: "/gallery" },
    { label: "Collections", href: "/gallery/collections" },
    { label: collection.name, isActive: true },
  ];

  // Format date
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Handle share
  const handleShare = async () => {
    const shareData = {
      title: collection.name,
      text: collection.description || `Check out this collection: ${collection.name}`,
      url: window.location.href,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  // Handle like toggle
  const handleLike = () => {
    setIsLiked(!isLiked);
    // Here you would typically make an API call to save the like
  };

  // Generate gradient background
  const getGradient = () => {
    if (collection.color) {
      return `linear-gradient(135deg, ${collection.color}15, ${collection.color}05)`;
    }
    return "linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--accent) / 0.05))";
  };

  return (
    <div className="min-h-screen py-8">
      <div className="container">
        {/* Breadcrumb */}
        <GalleryBreadcrumb items={breadcrumbItems} />

        {/* Collection Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12"
        >
          {/* Hero Section */}
          <div 
            className="relative rounded-3xl p-8 md:p-12 mb-8 overflow-hidden"
            style={{ background: getGradient() }}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-4 right-4">
                <Grid3X3 className="w-32 h-32 text-current rotate-12" />
              </div>
              <div className="absolute bottom-4 left-4">
                <Sparkles className="w-24 h-24 text-current -rotate-12" />
              </div>
            </div>

            <div className="relative z-10">
              <div className="flex flex-col lg:flex-row gap-8 items-start">
                {/* Collection Info */}
                <div className="flex-1">
                  {/* Icon and Title */}
                  <div className="flex items-start gap-4 mb-6">
                    <motion.div
                      initial={{ scale: 0, rotate: -45 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ duration: 0.6, delay: 0.2 }}
                      className={cn(
                        "flex items-center justify-center w-16 h-16 rounded-2xl",
                        "bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm",
                        "border border-white/20"
                      )}
                      style={{
                        backgroundColor: collection.color ? `${collection.color}20` : undefined,
                        borderColor: collection.color ? `${collection.color}40` : undefined,
                      }}
                    >
                      <Tag 
                        className="w-8 h-8" 
                        style={{ color: collection.color || "hsl(var(--primary))" }}
                      />
                    </motion.div>

                    <div className="flex-1">
                      <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                        {collection.name}
                      </h1>
                      
                      {collection.description && (
                        <p className="text-muted-foreground text-lg leading-relaxed mb-6">
                          {collection.description}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Metadata */}
                  <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mb-6">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(collection.createdAt)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ImageIcon className="w-4 h-4" />
                      <span>{collection.imageCount} images</span>
                    </div>
                    {collection.color && (
                      <div className="flex items-center gap-2">
                        <Palette className="w-4 h-4" />
                        <span>Themed Collection</span>
                      </div>
                    )}
                  </div>

                  {/* Badges */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    <Badge 
                      variant="secondary"
                      className="bg-white/10 backdrop-blur-sm text-foreground border-white/20"
                    >
                      <Tag className="w-3 h-3 mr-1" />
                      Collection
                    </Badge>
                    <Badge 
                      variant="secondary"
                      className="bg-white/10 backdrop-blur-sm text-foreground border-white/20"
                    >
                      <ImageIcon className="w-3 h-3 mr-1" />
                      {collection.imageCount} Images
                    </Badge>
                    {collection.color && (
                      <Badge 
                        variant="secondary"
                        className="bg-white/10 backdrop-blur-sm text-foreground border-white/20"
                      >
                        <div
                          className="w-3 h-3 rounded-full mr-1"
                          style={{ backgroundColor: collection.color }}
                        />
                        Themed
                      </Badge>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={handleLike}
                      variant="secondary"
                      size="sm"
                      className={cn(
                        "flex items-center gap-2 bg-white/10 backdrop-blur-sm border-white/20",
                        isLiked && "text-red-500 border-red-500/50"
                      )}
                    >
                      <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
                      {isLiked ? "Liked" : "Like"}
                    </Button>

                    <Button
                      onClick={handleShare}
                      variant="secondary"
                      size="sm"
                      className="flex items-center gap-2 bg-white/10 backdrop-blur-sm border-white/20"
                    >
                      <Share2 className="w-4 h-4" />
                      Share
                    </Button>

                    <Button 
                      asChild 
                      variant="ghost" 
                      size="sm"
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <Link href="/gallery/collections" className="flex items-center gap-2">
                        <ArrowLeft className="w-4 h-4" />
                        Back to Collections
                      </Link>
                    </Button>
                  </div>
                </div>

                {/* Color Indicator (if collection has a color) */}
                {collection.color && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="flex flex-col items-center gap-3"
                  >
                    <div className="text-sm text-muted-foreground">Collection Theme</div>
                    <div
                      className="w-20 h-20 rounded-full border-4 border-white/30 shadow-lg"
                      style={{ backgroundColor: collection.color }}
                    />
                    <div className="text-xs text-muted-foreground font-mono">
                      {collection.color.toUpperCase()}
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Images Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-3">
              <Grid3X3 className="w-6 h-6 text-primary" />
              Collection Gallery
            </h2>
            <p className="text-muted-foreground">
              Explore the curated images in this collection. Click on any image to view it in full size.
            </p>
          </div>

          {imagesLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Array.from({ length: 12 }).map((_, i) => (
                <Skeleton key={i} className="aspect-[4/3] rounded-xl" />
              ))}
            </div>
          ) : images.length > 0 ? (
            <MasonryGallery
              images={images}
              columns={4}
              gap={16}
              enableLightbox={true}
              showLoadingStates={true}
            />
          ) : (
            <div className="text-center py-20">
              <Tag className="w-20 h-20 mx-auto mb-6 text-muted-foreground/50" />
              <h3 className="text-2xl font-semibold text-foreground mb-4">
                No Images Found
              </h3>
              <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                This collection doesn't contain any images yet. Check back later for updates.
              </p>
              <Button asChild variant="outline">
                <Link href="/gallery/collections">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Collections
                </Link>
              </Button>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
