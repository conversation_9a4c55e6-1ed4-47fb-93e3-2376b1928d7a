import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { getCollectionById } from "@/lib/services/collection-service";
import CollectionPageContent from "./collection-page-content";

interface CollectionPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: CollectionPageProps): Promise<Metadata> {
  try {
    const collection = await getCollectionById(params.id);
    
    if (!collection) {
      return {
        title: "Collection Not Found - Astral Studios",
        description: "The requested collection could not be found.",
      };
    }

    return {
      title: `${collection.name} - Collections - Astral Studios`,
      description: collection.description || `Explore the ${collection.name} collection featuring ${collection.imageCount} curated photographs by Astral Studios.`,
      keywords: ["collection", "photography", collection.name, "curated", "astral studios"],
      openGraph: {
        title: `${collection.name} - Astral Studios`,
        description: collection.description || `Explore the ${collection.name} collection featuring ${collection.imageCount} curated photographs by Astral Studios.`,
        type: "website",
        images: [
          {
            url: "/images/collection-default.jpg", // You might want to add a cover image field to collections
            width: 1200,
            height: 630,
            alt: collection.name,
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title: `${collection.name} - Astral Studios`,
        description: collection.description || `Explore the ${collection.name} collection featuring ${collection.imageCount} curated photographs by Astral Studios.`,
        images: ["/images/collection-default.jpg"],
      },
    };
  } catch (error) {
    return {
      title: "Collection - Astral Studios",
      description: "View collection from Astral Studios photography gallery.",
    };
  }
}

export default async function CollectionPage({ params }: CollectionPageProps) {
  const collection = await getCollectionById(params.id);

  if (!collection) {
    notFound();
  }

  return (
    <Suspense fallback={<div>Loading collection...</div>}>
      <CollectionPageContent collection={collection} />
    </Suspense>
  );
}
