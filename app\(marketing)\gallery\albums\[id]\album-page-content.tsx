"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Calendar, 
  Image as ImageIcon, 
  Lock, 
  Share2, 
  Download, 
  Heart,
  ArrowLeft,
  Eye,
  Clock
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useImages } from "@/lib/hooks/use-images";
import { AlbumWithStats } from "@/lib/models";
import {
  GalleryBreadcrumb,
  MasonryGallery,
} from "@/components/gallery-marketing";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface AlbumPageContentProps {
  album: AlbumWithStats;
}

export default function AlbumPageContent({ album }: AlbumPageContentProps) {
  const [password, setPassword] = useState("");
  const [isUnlocked, setIsUnlocked] = useState(!album.hasPassword);
  const [passwordError, setPasswordError] = useState("");
  const [isLiked, setIsLiked] = useState(false);

  // Fetch images for this album
  const { 
    data: imagesData, 
    isLoading: imagesLoading, 
    error: imagesError 
  } = useImages({ 
    albumId: album._id as string,
    limit: 50 // Load more images for album view
  });

  const images = imagesData?.data || [];

  // Breadcrumb items
  const breadcrumbItems = [
    { label: "Gallery", href: "/gallery" },
    { label: "Albums", href: "/gallery/albums" },
    { label: album.name, isActive: true },
  ];

  // Format date
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Handle password submission
  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (password === album.password) {
      setIsUnlocked(true);
      setPasswordError("");
    } else {
      setPasswordError("Incorrect password. Please try again.");
    }
  };

  // Handle share
  const handleShare = async () => {
    const shareData = {
      title: album.name,
      text: album.description || `Check out this album: ${album.name}`,
      url: window.location.href,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  // Handle like toggle
  const handleLike = () => {
    setIsLiked(!isLiked);
    // Here you would typically make an API call to save the like
  };

  // Password protection dialog
  if (album.hasPassword && !isUnlocked) {
    return (
      <div className="min-h-screen py-8">
        <div className="container">
          <GalleryBreadcrumb items={breadcrumbItems} />
          
          <div className="max-w-md mx-auto mt-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center p-8 bg-card rounded-2xl border"
            >
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Lock className="w-8 h-8 text-primary" />
              </div>
              
              <h1 className="text-2xl font-bold text-foreground mb-2">
                Protected Album
              </h1>
              <p className="text-muted-foreground mb-6">
                This album is password protected. Please enter the password to view the content.
              </p>

              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter album password"
                    className="mt-1"
                  />
                  {passwordError && (
                    <p className="text-destructive text-sm mt-2">{passwordError}</p>
                  )}
                </div>
                
                <Button type="submit" className="w-full">
                  Unlock Album
                </Button>
              </form>

              <div className="mt-6 pt-6 border-t">
                <Button asChild variant="ghost">
                  <Link href="/gallery/albums" className="flex items-center gap-2">
                    <ArrowLeft className="w-4 h-4" />
                    Back to Albums
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container">
        {/* Breadcrumb */}
        <GalleryBreadcrumb items={breadcrumbItems} />

        {/* Album Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12"
        >
          <div className="flex flex-col lg:flex-row gap-8 items-start">
            {/* Album Info */}
            <div className="flex-1">
              <div className="flex items-start gap-4 mb-6">
                <div className="flex-1">
                  <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    {album.name}
                  </h1>
                  
                  {album.description && (
                    <p className="text-muted-foreground text-lg leading-relaxed mb-6">
                      {album.description}
                    </p>
                  )}

                  {/* Metadata */}
                  <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mb-6">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(album.createdAt)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ImageIcon className="w-4 h-4" />
                      <span>{album.imageCount} photos</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      <span>Public Album</span>
                    </div>
                  </div>

                  {/* Badges */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    <Badge variant="secondary">
                      <ImageIcon className="w-3 h-3 mr-1" />
                      {album.imageCount} Photos
                    </Badge>
                    {album.hasPassword && (
                      <Badge variant="outline">
                        <Lock className="w-3 h-3 mr-1" />
                        Protected
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={handleLike}
                  variant="outline"
                  size="sm"
                  className={cn(
                    "flex items-center gap-2",
                    isLiked && "text-red-500 border-red-500"
                  )}
                >
                  <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
                  {isLiked ? "Liked" : "Like"}
                </Button>

                <Button
                  onClick={handleShare}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Share2 className="w-4 h-4" />
                  Share
                </Button>

                <Button asChild variant="ghost" size="sm">
                  <Link href="/gallery/albums" className="flex items-center gap-2">
                    <ArrowLeft className="w-4 h-4" />
                    Back to Albums
                  </Link>
                </Button>
              </div>
            </div>

            {/* Cover Image */}
            {album.coverImageUrl && (
              <div className="w-full lg:w-80 xl:w-96">
                <div className="relative aspect-[4/3] rounded-2xl overflow-hidden border">
                  <img
                    src={album.coverImageUrl}
                    alt={album.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* Images Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-4">
              Album Gallery
            </h2>
            <p className="text-muted-foreground">
              Click on any image to view it in full size with our interactive lightbox.
            </p>
          </div>

          {imagesLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Array.from({ length: 12 }).map((_, i) => (
                <Skeleton key={i} className="aspect-[4/3] rounded-xl" />
              ))}
            </div>
          ) : images.length > 0 ? (
            <MasonryGallery
              images={images}
              columns={4}
              gap={16}
              enableLightbox={true}
              showLoadingStates={true}
            />
          ) : (
            <div className="text-center py-20">
              <ImageIcon className="w-20 h-20 mx-auto mb-6 text-muted-foreground/50" />
              <h3 className="text-2xl font-semibold text-foreground mb-4">
                No Images Found
              </h3>
              <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                This album doesn't contain any images yet. Check back later for updates.
              </p>
              <Button asChild variant="outline">
                <Link href="/gallery/albums">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Albums
                </Link>
              </Button>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
