"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { Calendar, Image as ImageIcon, Lock, Eye, Heart } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AlbumWithStats } from "@/lib/models";

interface AlbumCardProps {
  album: AlbumWithStats;
  className?: string;
  variant?: "default" | "compact" | "featured";
  showStats?: boolean;
  showDescription?: boolean;
  onLike?: (albumId: string) => void;
  isLiked?: boolean;
}

export default function AlbumCard({
  album,
  className,
  variant = "default",
  showStats = true,
  showDescription = true,
  onLike,
  isLiked = false,
}: AlbumCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const cardVariants = {
    default: "aspect-[4/3]",
    compact: "aspect-square",
    featured: "aspect-[16/9]",
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className={cn(
        "group relative overflow-hidden rounded-2xl bg-card border border-border",
        "hover:shadow-2xl hover:shadow-primary/20 transition-all duration-500",
        "hover:border-primary/50 hover:-translate-y-2",
        className
      )}
    >
      {/* Image Container */}
      <div className={cn("relative overflow-hidden", cardVariants[variant])}>
        {/* Cover Image */}
        {album.coverImageUrl ? (
          <Image
            src={album.coverImageUrl}
            alt={album.name}
            fill
            className={cn(
              "object-cover transition-all duration-700",
              "group-hover:scale-110",
              imageLoaded ? "opacity-100" : "opacity-0"
            )}
            onLoad={() => setImageLoaded(true)}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
            <ImageIcon className="w-16 h-16 text-muted-foreground/50" />
          </div>
        )}

        {/* Loading Skeleton */}
        {!imageLoaded && album.coverImageUrl && (
          <div className="absolute inset-0 bg-muted animate-pulse" />
        )}

        {/* Overlay Gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

        {/* Password Protection Badge */}
        {album.hasPassword && (
          <div className="absolute top-3 right-3">
            <Badge variant="secondary" className="bg-black/50 text-white border-white/20">
              <Lock className="w-3 h-3 mr-1" />
              Protected
            </Badge>
          </div>
        )}

        {/* Image Count Badge */}
        {showStats && (
          <div className="absolute top-3 left-3">
            <Badge variant="secondary" className="bg-black/50 text-white border-white/20">
              <ImageIcon className="w-3 h-3 mr-1" />
              {album.imageCount}
            </Badge>
          </div>
        )}

        {/* Hover Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 20 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <div className="flex gap-2">
            <Button
              asChild
              size="sm"
              className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
            >
              <Link href={`/gallery/albums/${album._id}`}>
                <Eye className="w-4 h-4 mr-2" />
                View Album
              </Link>
            </Button>
            
            {onLike && (
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault();
                  onLike(album._id as string);
                }}
                className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
              >
                <Heart className={cn("w-4 h-4", isLiked && "fill-current text-red-500")} />
              </Button>
            )}
          </div>
        </motion.div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Title */}
        <Link href={`/gallery/albums/${album._id}`}>
          <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors duration-200 line-clamp-2">
            {album.name}
          </h3>
        </Link>

        {/* Description */}
        {showDescription && album.description && (
          <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
            {album.description}
          </p>
        )}

        {/* Metadata */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            <span>{formatDate(album.createdAt)}</span>
          </div>
          
          {showStats && (
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <ImageIcon className="w-3 h-3" />
                {album.imageCount} photos
              </span>
            </div>
          )}
        </div>

        {/* Progress Bar (if applicable) */}
        {variant === "featured" && (
          <div className="mt-4">
            <div className="w-full bg-muted rounded-full h-1">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: isHovered ? "100%" : "0%" }}
                transition={{ duration: 0.8, ease: "easeInOut" }}
                className="h-1 bg-gradient-accent rounded-full"
              />
            </div>
          </div>
        )}
      </div>

      {/* Decorative Elements */}
      <div className="absolute -top-20 -right-20 w-40 h-40 bg-primary/5 rounded-full blur-3xl group-hover:bg-primary/10 transition-colors duration-500" />
      <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-accent/5 rounded-full blur-2xl group-hover:bg-accent/10 transition-colors duration-500" />
    </motion.div>
  );
}
